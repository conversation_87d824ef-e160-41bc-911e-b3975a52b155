package com.suite.chatdatabi.attribution;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.fastjson.JSON;
import com.suite.chatdatabi.chat.server.service.GraphQueryService;
import com.suite.chatdatabi.enmu.ErrorType;
import com.suite.chatdatabi.headless.api.pojo.DataSetSchema;
import com.suite.chatdatabi.headless.api.pojo.SchemaElement;
import com.suite.chatdatabi.headless.api.pojo.SemanticSchema;

import java.util.List;
import java.util.Map;

public class SchemaNode implements NodeAction {

    private final GraphQueryService graphQueryService;

    public SchemaNode(GraphQueryService graphQueryService) {
        this.graphQueryService = graphQueryService;
    }

    @Override
    public Map<String, Object> apply(OverAllState state)  {
            try {
                Integer agentId = (Integer) state.value("agentId").orElse(null);
                SemanticSchema dataSetSchema = graphQueryService.getDataSetSchema(agentId);
                Map<Long, DataSetSchema> dataSetSchemaMap = dataSetSchema.getDataSetSchemaMap();
                return Map.of("schemaResult", JSON.toJSONString(dataSetSchemaMap));
            }catch (Exception e){
                return Map.of("error", ErrorType.SCHEMA_ERROR);
            }
    }
}
