package com.suite.chatdatabi.chat.server.service.impl;

import com.alibaba.fastjson.JSON;
import com.facebook.presto.jdbc.internal.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.nimbusds.jose.shaded.gson.Gson;
import com.suite.chatdatabi.chat.api.pojo.request.ChatExecuteReq;
import com.suite.chatdatabi.chat.api.pojo.request.ChatParseReq;
import com.suite.chatdatabi.chat.api.pojo.request.ChatQueryDataReq;
import com.suite.chatdatabi.chat.api.pojo.response.ChatParseResp;
import com.suite.chatdatabi.chat.api.pojo.response.QueryResp;
import com.suite.chatdatabi.chat.api.pojo.response.QueryResult;
import com.suite.chatdatabi.chat.server.agent.Agent;
import com.suite.chatdatabi.chat.server.executor.ChatQueryExecutor;
import com.suite.chatdatabi.chat.server.parser.ChatQueryParser;
import com.suite.chatdatabi.chat.server.persistence.dataobject.ChatQueryDO;
import com.suite.chatdatabi.chat.server.persistence.repository.ChatQueryRepository;
import com.suite.chatdatabi.chat.server.persistence.repository.ChatRepository;
import com.suite.chatdatabi.chat.server.pojo.ExecuteContext;
import com.suite.chatdatabi.chat.server.pojo.ParseContext;
import com.suite.chatdatabi.chat.server.processor.execute.ExecuteResultProcessor;
import com.suite.chatdatabi.chat.server.processor.parse.ParseResultProcessor;
import com.suite.chatdatabi.chat.server.processor.summary.DataInterpretProcessor;
import com.suite.chatdatabi.chat.server.processor.summary.SummaryProcessor;
import com.suite.chatdatabi.chat.server.service.AgentService;
import com.suite.chatdatabi.chat.server.service.ChatManageService;
import com.suite.chatdatabi.chat.server.service.ChatQueryService;
import com.suite.chatdatabi.chat.server.util.ChatParseEvent;
import com.suite.chatdatabi.chat.server.util.ComponentFactory;
import com.suite.chatdatabi.chat.server.util.ExecuteResultEvent;
import com.suite.chatdatabi.chat.server.util.QueryReqConverter;
import com.suite.chatdatabi.common.config.ChatModel;
import com.suite.chatdatabi.common.jsqlparser.*;
import com.suite.chatdatabi.common.pojo.ChatApp;
import com.suite.chatdatabi.common.pojo.ChatModelConfig;
import com.suite.chatdatabi.common.pojo.User;
import com.suite.chatdatabi.common.pojo.enums.FilterOperatorEnum;
import com.suite.chatdatabi.common.pojo.enums.Text2SQLType;
import com.suite.chatdatabi.common.service.ChatModelService;
import com.suite.chatdatabi.common.util.*;
import com.suite.chatdatabi.headless.api.pojo.*;
import com.suite.chatdatabi.headless.api.pojo.request.*;
import com.suite.chatdatabi.headless.api.pojo.response.*;
import com.suite.chatdatabi.headless.chat.knowledge.helper.HanlpHelper;
import com.suite.chatdatabi.headless.chat.query.QueryManager;
import com.suite.chatdatabi.headless.chat.query.SemanticQuery;
import com.suite.chatdatabi.headless.chat.query.llm.s2sql.LLMSqlQuery;
import com.suite.chatdatabi.headless.core.pojo.Ontology;
import com.suite.chatdatabi.headless.core.pojo.OntologyQuery;
import com.suite.chatdatabi.headless.core.pojo.QueryStatement;
import com.suite.chatdatabi.headless.server.facade.service.ChatLayerService;
import com.suite.chatdatabi.headless.server.facade.service.SemanticLayerService;
import com.suite.chatdatabi.headless.server.service.DataSetService;
import com.suite.chatdatabi.headless.server.service.RetrieveService;
import com.suite.chatdatabi.headless.server.service.SchemaService;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.relational.ComparisonOperator;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.ParenthesedExpressionList;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.select.ParenthesedSelect;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.WithItem;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 聊天查询服务实现类 负责处理自然语言查询的解析、执行和结果处理 包含对话查询的核心流程：解析自然语言、构建结构化查询、执行查询、处理结果
 */
@Slf4j
@Service
public class ChatQueryServiceImpl implements ChatQueryService {
    /**
     * 聊天管理服务，负责管理聊天记录和查询历史
     */
    @Autowired
    private ChatManageService chatManageService;

    @Autowired
    private ChatModelService chatModelService;
    /**
     * 聊天层服务，提供对话相关的核心功能
     */
    @Autowired
    private ChatLayerService chatLayerService;
    @Autowired
    private ChatQueryRepository chatQueryRepository;
    /**
     * 语义层服务，负责处理语义理解和数据查询
     */
    @Autowired
    private SemanticLayerService semanticLayerService;

    /**
     * 代理服务，管理聊天代理实例
     */
    @Autowired
    private AgentService agentService;

    @Autowired
    DataSetService dataSetService;

    @Autowired
    protected SchemaService schemaService;

    /**
     * 聊天查询解析器列表，用于解析自然语言查询
     */
    private final List<ChatQueryParser> chatQueryParsers = ComponentFactory.getChatParsers();

    /**
     * 聊天查询执行器列表，用于执行解析后的查询
     */
    private final List<ChatQueryExecutor> chatQueryExecutors = ComponentFactory.getChatExecutors();

    /**
     * 聊天查询执行器列表，用于执行解析后的查询
     */
    private final List<SummaryProcessor> summaryProcessors =
            ComponentFactory.getSummaryProcessors();
    /**
     * 解析结果处理器列表，处理查询解析结果
     */
    private final List<ParseResultProcessor> parseResultProcessors =
            ComponentFactory.getParseProcessors();

    /**
     * 执行结果处理器列表，处理查询执行结果
     */
    private final List<ExecuteResultProcessor> executeResultProcessors =
            ComponentFactory.getExecuteProcessors();

    /**
     * 搜索相关查询 基于自然语言查询文本，检索相关的搜索结果 如果代理启用了搜索功能，则构建查询请求并执行检索
     *
     * @param chatParseReq 聊天解析请求，包含查询文本和相关参数
     * @return 搜索结果列表
     */
    @Override
    public List<SearchResult> search(ChatParseReq chatParseReq) {
        // 通过前端传入的agentId获取agent对象
        ParseContext parseContext = buildParseContext(chatParseReq, null);

        Agent agent = parseContext.getAgent();
        // 如果agent不支持搜索功能，则直接返回空列表，目前都返回true
        if (!agent.enableSearch()) {
            return Lists.newArrayList();
        }
        QueryNLReq queryNLReq = QueryReqConverter.buildQueryNLReq(parseContext);
        return chatLayerService.retrieve(queryNLReq);
    }

    /**
     * 解析自然语言查询 将用户输入的自然语言转换为结构化的查询信息
     *
     * 处理流程： 1. 如果没有查询ID，则创建新的查询记录 2. 构建解析上下文 3. 依次使用适合的解析器处理查询 4. 依次应用解析结果处理器 5. 如不需要反馈，则保存解析结果和耗时
     *
     * @param chatParseReq 聊天解析请求
     * @return 解析结果，包含查询ID和解析后的结构化信息
     */
    @Override
    public ChatParseResp parse(ChatParseReq chatParseReq) {
        // 增加获取意图识别接口，区分是问数还是dify
        ChatParseResp response = null;
        Long queryId = chatParseReq.getQueryId();
        // 如果没有查询ID，则创建新的查询记录
        if (Objects.isNull(queryId)) {
            queryId = chatManageService.createChatQuery(chatParseReq);
            chatParseReq.setQueryId(queryId);
        }
        // 构建解析上下文，生成新的构建对象，增加查询ID、agent对象信息
        ParseContext parseContext = buildParseContext(chatParseReq, new ChatParseResp(queryId));
        ObjectMapper objectMapper = new ObjectMapper();
        // 依次使用适合的解析器处理查询
        for (ChatQueryParser parser : chatQueryParsers) {
            boolean accepted = parser.accept(parseContext);
            if (accepted) {
                long startTime = System.currentTimeMillis();
                parser.parse(parseContext);
                Map<String, String> difyParmes = new HashMap<String, String>();
                if ("DIFY".equals(parseContext.getAgent().getIntentType())){
//                  //这个地方需要注意一下，这里是获取当前助理管理data_query_type配置的dify的url、apikey、和协议类型
                    Agent agent = agentService.getAgent(chatParseReq.getAgentId());
                    Map<String, ChatApp> chatAppConfig = agent.getChatAppConfig();
                    ChatApp chatApp = chatAppConfig.get("SMALL_TALK");
                    boolean enable = chatApp.isEnable();
                    ChatModelConfig chatModelConfig = new ChatModelConfig();
                    if (enable) {
                        chatModelConfig = chatApp.getChatModelConfig();
                        difyParmes.put("baseUrl",chatModelConfig.getBaseUrl());
                        difyParmes.put("apiKey",chatModelConfig.getApiKey());
                        difyParmes.put("provider",chatModelConfig.getProvider());
                        response = new ChatParseResp(difyParmes);
                        return response;
                    }else {
                        Map<String, String> difyMeg = new HashMap<String, String>();
                        difyMeg.put("message","请打开闲聊对话开关按钮，并且闲聊对话模型需要配置dify模型");
                        response = new ChatParseResp(difyMeg);
                        return response;
                    }
                }
                ChatQueryDO queryDO = chatQueryRepository.getChatQueryDO(queryId);
                log.info("=============意图分类" + parseContext.getAgent().getIntentType());
                if ("归因分析".equals(parseContext.getAgent().getIntentType())){
                    difyParmes.put("data_query_type","归因分析");
                    response = new ChatParseResp(difyParmes);
                    try {
                        String json = objectMapper.writeValueAsString(difyParmes);
                        queryDO.setDifyParmes(json);
                        chatQueryRepository.updateChatQuery(queryDO);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return response;
                }
                if ("生成报告".equals(parseContext.getAgent().getIntentType())){
                    difyParmes.put("data_query_type","生成报告");
                    response = new ChatParseResp(difyParmes);
                    try {
                        String json = objectMapper.writeValueAsString(difyParmes);
                        queryDO.setDifyParmes(json);
                        chatQueryRepository.updateChatQuery(queryDO);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return response;
                }
                log.info("获取解析后保存内容", parseContext.getAgent().getIntentType());
                long endTime = System.currentTimeMillis();
                log.info("解析器 {} 处理完成，耗时: {}ms，解析状态: {}", parser.getClass().getSimpleName(),
                        (endTime - startTime), parseContext.getResponse().getState());
            }
        }

        for (ParseResultProcessor processor : parseResultProcessors) {
            boolean accepted = processor.accept(parseContext);
            if (accepted) {
                processor.process(parseContext);
            }
        }

        if (!parseContext.needFeedback()) {
            chatManageService.batchAddParse(chatParseReq, parseContext.getResponse());
            chatManageService.updateParseCostTime(parseContext.getResponse());
        }
        response = parseContext.getResponse();
        Map<String, String> difyParmes = new HashMap<String, String>();
        difyParmes.put("data_query_type","数据查询");
        ChatQueryDO queryD = chatQueryRepository.getChatQueryDO(queryId);
        try {
            String json = objectMapper.writeValueAsString(difyParmes);
            queryD.setDifyParmes(json);
            chatQueryRepository.updateChatQuery(queryD);
        } catch (Exception e) {
            e.printStackTrace();
        }
        response.setDifyParmes(difyParmes);
        response.setFilterText(response.transferResolvingText());
        //修改第一次提问内容为对话名称
        List<QueryResp> chatQueryDOS = chatManageService.getChatQueries(chatParseReq.getChatId());
        //查询对话
        if (chatQueryDOS.size() == 1) {
            chatManageService.updateChatName(Long.valueOf(chatParseReq.getChatId()), chatParseReq.getQueryText(),chatParseReq.getUser().getName());
        }
        return response;
    }

    /**
     *
     * @param chatParseReq
     * @return
     */
    @Override
    public String nl2Data(ChatParseReq chatParseReq) {

        ChatParseResp response = null;

        // 构建解析上下文，生成新的构建对象，增加查询ID、agent对象信息
        ParseContext parseContext = buildParseContext(chatParseReq, new ChatParseResp(chatParseReq.getQueryId()));
        for (ChatQueryParser parser : chatQueryParsers) {
            boolean accepted = parser.accept(parseContext);
            if (accepted) {
                parser.parse(parseContext);
            }
        }
        for (ParseResultProcessor processor : parseResultProcessors) {
            boolean accepted = processor.accept(parseContext);
            if (accepted) {
                processor.process(parseContext);
            }
        }

        response = parseContext.getResponse();
        ChatExecuteReq executeReq = new ChatExecuteReq();
        executeReq.setQueryId(chatParseReq.getQueryId());
        SemanticParseInfo semanticParseInfo = response.getSelectedParses().get(0);
        executeReq.setParseId(semanticParseInfo.getId());
        executeReq.setQueryText(chatParseReq.getQueryText());
        executeReq.setChatId(chatParseReq.getChatId());
        executeReq.setUser(chatParseReq.getUser());
        executeReq.setAgentId(chatParseReq.getAgentId());

        if(response.getSelectedParses() != null && !response.getSelectedParses().isEmpty()){
            String sql = semanticParseInfo.getSqlInfo().getQuerySQL();
            if(StringUtils.isNotBlank(sql)){
                QueryResult execute = execute(executeReq, semanticParseInfo);
                Map<String,Object> resultMap = new HashMap<>();
                resultMap.put(chatParseReq.getQueryText(),execute.getQueryResults());
                return JSON.toJSONString(resultMap);
            }
        }

        return null;
    }

    @Override
    public Flux<ChatParseEvent> difyParse(ChatParseReq chatParseReq) {

        // 创建一个多播的Sink，用于发布事件
        Sinks.Many<ChatParseEvent> sink = Sinks.many().multicast().onBackpressureBuffer();

        // 异步处理解析逻辑
        CompletableFuture.runAsync(() -> {
            try {
                log.info("开始解析查询，queryText: {}, chatId: {}, agentId: {}",
                        chatParseReq.getQueryText(), chatParseReq.getChatId(),
                        chatParseReq.getAgentId());

                // 1. 创建查询ID
                Long queryId = chatParseReq.getQueryId();
                if (Objects.isNull(queryId)) {
                    log.debug("未提供queryId，创建新的查询记录");
                    queryId = chatManageService.createChatQuery(chatParseReq);
                    chatParseReq.setQueryId(queryId);
                    log.debug("创建新的查询记录成功，queryId: {}", queryId);

                    // 发布查询创建事件
                    sink.tryEmitNext(new ChatParseEvent("QUERY_CREATED", queryId,
                            System.currentTimeMillis(), null));
                }

                // 1. 准备请求
                ChatExecuteReq chatExecuteReq = new ChatExecuteReq();
                BeanUtils.copyProperties(chatParseReq, chatExecuteReq);
                String input = null;
                // try {
                // input = getChatHistoryQuery(chatExecuteReq);
                // } catch (Exception e) {
                // throw new RuntimeException(e);
                // }
                ParseContext parseContext =
                        buildParseContext(chatParseReq, new ChatParseResp(queryId));

                DifyRequest request = new DifyRequest();
                request.setQuery(chatParseReq.getQueryText());
                request.setUser(chatParseReq.getUser().getName());
                request.setResponseMode("streaming"); // 确保开启流式模式

                // 2. 获取模型配置
                Agent agent = agentService.getAgent(chatExecuteReq.getAgentId());
                ChatModel chatModel = chatModelService
                        .getChatModel(agent.getChatAppConfig().get("SMALL_TALK").getChatModelId());
                // 构建Dify请求
                DifyRequest difyRequest = new DifyRequest();
                difyRequest.setQuery(chatParseReq.getQueryText());
                difyRequest.setUser(chatParseReq.getUser().getName());
                difyRequest.setResponseMode("streaming"); // 确保使用流式模式

                String jsonBody = new Gson().toJson(difyRequest);
                MediaType JSON = MediaType.get("application/json");
                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", "Bearer " + chatModel.getConfig().getApiKey());
                headers.put("Content-Type", "application/json");
                headers.put("Accept", "text/event-stream");
                log.info("BaseUrl: {}", chatModel.getConfig().getBaseUrl());
                // 创建HTTP请求
                Request httpRequest = new Request.Builder().url(chatModel.getConfig().getBaseUrl())
                        .post(RequestBody.create(jsonBody, JSON)).headers(Headers.of(headers))
                        .build();

                sink.tryEmitNext(new ChatParseEvent("DIFY_CONNECTION_START", queryId,
                        System.currentTimeMillis(),
                        Map.of("apiEndpoint", chatModel.getConfig().getBaseUrl())));
                OkHttpClient client =
                        new OkHttpClient.Builder().connectTimeout(30, TimeUnit.SECONDS) // 连接超时30秒
                                .readTimeout(120, TimeUnit.SECONDS) // 读取超时120秒
                                .writeTimeout(30, TimeUnit.SECONDS) // 发送超时30秒
                                .build();
                // 3. 调用Dify API并处理流式响应
                try (Response response = client.newCall(httpRequest).execute()) {
                    if (!response.isSuccessful()) {
                        String errorMsg =
                                "Dify API 错误: " + response.code() + " - " + response.message();
                        log.error(errorMsg);
                        sink.tryEmitNext(new ChatParseEvent("DIFY_API_ERROR", queryId,
                                System.currentTimeMillis(),
                                Map.of("status", response.code(), "message", response.message())));
                        sink.tryEmitComplete();
                        return;
                    }

                    sink.tryEmitNext(new ChatParseEvent("DIFY_CONNECTION_SUCCESS", queryId,
                            System.currentTimeMillis(), Map.of("status", response.code())));

                    // 处理流式响应
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(response.body().byteStream()))) {
                        log.info("--------BufferedReader----------> {}");
                        String line;

                        while ((line = reader.readLine()) != null) {
                            String data = line.substring(6).trim();
                            log.info("lineData: {}", line);
                            // 检查流结束标记
                            if ("[DONE]".equals(data)) {
                                log.info("Dify 流式响应结束");
                                break;
                            }
                            try {
                                // // 实时发送当前内容片段
                                // sink.tryEmitNext(new ChatParseEvent(
                                // "DIFY_MESSAGE", // 使用实际事件类型
                                // queryId,
                                // System.currentTimeMillis(),
                                // Map.of(
                                // "content", data,
                                // "delta", true
                                // )
                                // ));
                                for (char c : line.toCharArray()) {
                                    sink.tryEmitNext(new ChatParseEvent("DIFY_MESSAGE", queryId,
                                            System.currentTimeMillis(),
                                            Map.of("content", String.valueOf(c), "delta", true)));
                                }

                            } catch (Exception e) {
                                log.warn("Dify 响应解析失败: {}", data, e);
                            }
                        }

                        // 4. 保存完整响应
                        // String finalResponse = fullResponse.toString();
                        // log.info("Dify解析完成，queryId: {}, 响应长度: {}", queryId,
                        // finalResponse.length());

                        sink.tryEmitNext(new ChatParseEvent("DIFY_RESPONSE_COMPLETE", queryId,
                                System.currentTimeMillis(), Map.of("status", "success")));
                        for (ChatQueryParser parser : chatQueryParsers) {
                            boolean accepted = parser.accept(parseContext);
                            log.debug("检查解析器 {} 是否接受处理: {}", parser.getClass().getSimpleName(),
                                    accepted);
                            if (accepted) {
                                log.info("解析器 {} 开始处理查询", parser.getClass().getSimpleName());
                                long startTime = System.currentTimeMillis();

                                // 同步解析或异步解析（取决于解析器实现）
                                parser.parse(parseContext);

                                long endTime = System.currentTimeMillis();
                                log.info("解析器 {} 处理完成，耗时: {}ms，解析状态: {}",
                                        parser.getClass().getSimpleName(), (endTime - startTime),
                                        parseContext.getResponse().getState());

                                // 发布解析器处理事件
                                sink.tryEmitNext(new ChatParseEvent("PARSER_PROCESSED", queryId,
                                        endTime,
                                        Map.of("parser", parser.getClass().getSimpleName(),
                                                "processingTime", endTime - startTime, "state",
                                                parseContext.getResponse().getState())));
                            }
                        }
                        for (ParseResultProcessor processor : parseResultProcessors) {
                            boolean accepted = processor.accept(parseContext);
                            log.debug("检查处理器 {} 是否接受处理: {}", processor.getClass().getSimpleName(),
                                    accepted);
                            if (accepted) {
                                log.debug("处理器 {} 开始处理解析结果", processor.getClass().getSimpleName());
                                processor.process(parseContext);
                                log.debug("处理器 {} 处理完成", processor.getClass().getSimpleName());
                            }
                        }

                        // 创建解析结果
                        // ChatParseResp parseResp = new ChatParseResp(queryId);
                        if (!parseContext.needFeedback()) {
                            // 保存解析结果
                            chatManageService.batchAddParse(chatParseReq,
                                    parseContext.getResponse());
                            chatManageService.updateParseCostTime(parseContext.getResponse());
                        }
                        sink.tryEmitNext(new ChatParseEvent("RESULT_SAVED", queryId,
                                System.currentTimeMillis(),
                                Map.of("processingTime", parseContext.getResponse())));

                        // 最终完成事件
                        sink.tryEmitNext(new ChatParseEvent("PARSE_COMPLETED", queryId,
                                System.currentTimeMillis(), response));
                    }
                } catch (IOException e) {
                    log.error("Dify连接错误", e);
                    sink.tryEmitNext(new ChatParseEvent("DIFY_CONNECTION_ERROR", queryId,
                            System.currentTimeMillis(), Map.of("error", e.getMessage())));
                }
                // 标记流完成
                sink.tryEmitComplete();
            } catch (Exception e) {
                log.error("解析过程发生异常", e);
                // 发布错误事件
                sink.tryEmitError(e);
            }
        });

        // 返回Flux流
        return sink.asFlux();
    }

    /**
     * 执行解析后的查询 根据解析结果执行实际的数据查询操作
     *
     * 处理流程： 1. 构建执行上下文 2. 使用适合的执行器执行查询 3. 依次应用执行结果处理器 4. 保存查询结果
     *
     * @param chatExecuteReq 查询执行请求
     * @return 查询结果，包含数据和元数据
     */
    @Override
    public QueryResult execute(ChatExecuteReq chatExecuteReq) {
        TimeStatUtils timeStatUtils = new TimeStatUtils();
        timeStatUtils.start();
        QueryResult queryResult = new QueryResult();
        ExecuteContext executeContext = buildExecuteContext(chatExecuteReq);

//        log.info("开始执行查询，chatExecuteReq 请求参数: {}", chatExecuteReq);
//        log.info("开始执行查询，executeContext 请求参数: {}", executeContext);

        for (ChatQueryExecutor chatQueryExecutor : chatQueryExecutors) {
            if (chatQueryExecutor.accept(executeContext)) {
                queryResult = chatQueryExecutor.execute(executeContext);
                if (queryResult != null) {
                    break;
                }
            }
        }

        executeContext.setResponse(queryResult);
        if (queryResult != null) {
            for (ExecuteResultProcessor processor : executeResultProcessors) {
                if (processor.accept(executeContext)) {
                    processor.process(executeContext);
                }
            }
            saveQueryResult(chatExecuteReq, queryResult);

            timeStatUtils.end();
            queryResult.setQueryTimeCost(timeStatUtils.getTime());
            log.info("执行查询耗时: {} ms", timeStatUtils.getTime());
        }

        return queryResult;
    }
    private QueryResult execute(ChatExecuteReq chatExecuteReq,SemanticParseInfo semanticParseInfo) {
        TimeStatUtils timeStatUtils = new TimeStatUtils();
        timeStatUtils.start();
        QueryResult queryResult = new QueryResult();
        ExecuteContext executeContext = buildExecuteContext(chatExecuteReq,semanticParseInfo);

        for (ChatQueryExecutor chatQueryExecutor : chatQueryExecutors) {
            if (chatQueryExecutor.accept(executeContext)) {
                queryResult = chatQueryExecutor.execute(executeContext);
                if (queryResult != null) {
                    break;
                }
            }
        }

        executeContext.setResponse(queryResult);
        if (queryResult != null) {
            for (ExecuteResultProcessor processor : executeResultProcessors) {
                if (processor.accept(executeContext)) {
                    processor.process(executeContext);
                }
            }
        }

        return queryResult;
    }
    @Override
    public String getChatHistoryQuery(ChatExecuteReq chatExecuteReq) throws Exception {
        AgentService agentService = ContextUtils.getBean(AgentService.class);
        Agent chatAgent = agentService.getAgent(chatExecuteReq.getAgentId());
        ChatApp chatApp = chatAgent.getChatAppConfig().get("SMALL_TALK");
        if (Objects.isNull(chatApp) || !chatApp.isEnable()) {
            return null;
        }
        StringBuilder historyInput = new StringBuilder();
        ChatManageService chatManageService = ContextUtils.getBean(ChatManageService.class);
        List<QueryResp> contextualParseInfoList =
                chatManageService.getChatQueriesDify(chatExecuteReq.getChatId()).stream()
                        .filter(q -> Objects.nonNull(q.getQueryResult())
                                && q.getQueryResult().getQueryState() == QueryState.SUCCESS)
                        .collect(Collectors.toList());
        List<QueryResp> contextualList =
                contextualParseInfoList.subList(0, Math.min(3, contextualParseInfoList.size()));
        Collections.reverse(contextualList);
        List<Map<String, String>> mapList = new ArrayList<>();
        for (QueryResp p : contextualList) {
            // 计算当前 historyInput 的 token 数量
            int currentTokenCount = estimateTokenCount(historyInput.toString());
            Map<String, String> map = new HashMap<>();
            // 计算新内容的 token 数量
            String newEntry = p.getQueryText() + "?" + p.getQueryResult().getTextResult() + ";";
            int newTokenCount = estimateTokenCount(newEntry);
            map.put("question", p.getQueryText());
            map.put("answer", p.getQueryResult().getTextResult());
            // 如果添加后超过 5000 tokens，则停止
            if (currentTokenCount + newTokenCount > 5000) {
                break;
            }
            mapList.add(map);
            // 否则，添加新内容
            historyInput.append(newEntry);
        }
        ObjectMapper objectMapper = new ObjectMapper();
        String json = objectMapper.writeValueAsString(mapList);

        String promptStr = String.format(chatApp.getPrompt(), json, chatExecuteReq.getQueryText());
        return promptStr;
    }

    @Override
    public Map<String, Object> saveDirectQA(Integer agentId, Long chatId, String queryText,
                                            User user, Integer parseId, QueryResult queryResult, String answer) {
        Map<String, Object> saveResult = new HashMap<String, Object>();
        // 创建 ChatParseReq 对象
        ChatParseReq chatParseReq = ChatParseReq.builder().chatId(chatId.intValue())
                .agentId(agentId).queryText(queryText).user(user).build();
        // 创建查询记录
        Long queryId = chatManageService.createChatQuery(chatParseReq);
        // 如果没有查询ID，则创建新的查询记录
        if (Objects.isNull(queryId)) {
            log.debug("未提供queryId，创建新的查询记录");
            queryId = chatManageService.createChatQuery(chatParseReq);
            chatParseReq.setQueryId(queryId);
            log.debug("创建新的查询记录成功，queryId: {}", queryId);
        }
        // 创建 QueryResult 对象
        QueryResult queryResult1 = new QueryResult();
        queryResult1.setTextResult(answer);
        queryResult1.setQueryState(QueryState.SUCCESS);
        queryResult1.setQueryMode("PLAIN_TEXT");
        queryResult1.setQueryId(chatParseReq.getQueryId());
        // 创建 ChatExecuteReq 对象
        ChatExecuteReq chatExecuteReq = ChatExecuteReq.builder().queryId(queryId)
                .chatId(chatId.intValue()).agentId(agentId).parseId(parseId).build();
        // 保存查询结果
        saveQueryResult(chatExecuteReq, queryResult1);
        chatManageService.updateLastQuestion(chatId,queryText);
        List<QueryResp> chatQueryDOS = chatManageService.getChatQueries(Math.toIntExact(chatId));
        //修改Dify第一次提问内容为对话名称(此处修改---)
        if (chatQueryDOS.size() == 2) {
            chatManageService.updateChatName(Long.valueOf(chatId), chatParseReq.getQueryText(),chatParseReq.getUser().getName());
        }
        return saveResult;
    }

    /**
     * 解析并执行查询（组合操作） 一次性完成自然语言解析和查询执行的组合操作
     *
     * 处理流程： 1. 先解析自然语言查询 2. 检查解析结果是否有可用的解析内容 3. 构建执行请求参数 4. 调用execute方法执行查询
     *
     * @param chatParseReq 聊天解析请求
     * @return 查询结果，如果没有有效解析则返回null
     */
    @Override
    public QueryResult parseAndExecute(ChatParseReq chatParseReq) {
        ChatParseResp parseResp = parse(chatParseReq);
        if (CollectionUtils.isEmpty(parseResp.getSelectedParses())) {
            log.debug("chatId:{}, agentId:{}, queryText:{}, parseResp.getSelectedParses() is empty",
                    chatParseReq.getChatId(), chatParseReq.getAgentId(),
                    chatParseReq.getQueryText());
            return null;
        }
        ChatExecuteReq executeReq = new ChatExecuteReq();
        executeReq.setQueryId(parseResp.getQueryId());
        executeReq.setParseId(parseResp.getSelectedParses().get(0).getId());
        executeReq.setQueryText(chatParseReq.getQueryText());
        executeReq.setChatId(chatParseReq.getChatId());
        executeReq.setUser(User.getDefaultUser());
        executeReq.setAgentId(chatParseReq.getAgentId());
        executeReq.setSaveAnswer(true);
        return execute(executeReq);
    }

    /**
     * 构建新构造parseContext对象，解析并且返回对象中的agent对象
     *
     * @param chatParseReq 聊天解析请求
     * @param chatParseResp 聊天解析响应（可为null）
     * @return 解析上下文对象
     */
    private ParseContext buildParseContext(ChatParseReq chatParseReq, ChatParseResp chatParseResp) {
        log.debug("开始构建ParseContext，请求文本: {}", chatParseReq.getQueryText());

        ParseContext parseContext = new ParseContext(chatParseReq, chatParseResp);
        // 通过前端传入的agentId获取agent对象
        Integer agentId = chatParseReq.getAgentId();
        log.debug("从请求中获取agentId: {}", agentId);

        Agent agent = agentService.getAgent(agentId);
        log.debug("获取到Agent: {}, 支持的数据集: {}, 包含数据集工具: {}, 包含插件工具: {}", agent.getName(),
                agent.getDataSetIds(), agent.containsDatasetTool(), agent.containsPluginTool());

        parseContext.setAgent(agent);

        if (chatParseResp != null) {
            log.debug("已提供ChatParseResp，queryId: {}", chatParseResp.getQueryId());
        } else {
            log.debug("未提供ChatParseResp，这可能是搜索请求");
        }

        log.debug("ParseContext构建完成");
        return parseContext;
    }



    /**
     * 构建执行上下文 为查询执行过程准备必要的上下文环境
     *
     * @param chatExecuteReq 聊天执行请求
     * @return 执行上下文对象
     */
    private ExecuteContext buildExecuteContext(ChatExecuteReq chatExecuteReq) {
        ExecuteContext executeContext = new ExecuteContext(chatExecuteReq);
        SemanticParseInfo parseInfo = chatManageService.getParseInfo(chatExecuteReq.getQueryId(),
                chatExecuteReq.getParseId());
        Agent agent = agentService.getAgent(chatExecuteReq.getAgentId());
        executeContext.setAgent(agent);
        executeContext.setParseInfo(parseInfo);
        return executeContext;
    }

    /**
     * 构建执行上下文 为查询执行过程准备必要的上下文环境
     *
     * @param chatExecuteReq 聊天执行请求
     * @return 执行上下文对象
     */
    private ExecuteContext buildExecuteContext(ChatExecuteReq chatExecuteReq, SemanticParseInfo semanticParseInfo) {
        ExecuteContext executeContext = new ExecuteContext(chatExecuteReq);

        Agent agent = agentService.getAgent(chatExecuteReq.getAgentId());
        executeContext.setAgent(agent);
        executeContext.setParseInfo(semanticParseInfo);
        return executeContext;
    }
    private ExecuteContext buildExeContext(ChatExecuteReq chatExecuteReq) {
        ExecuteContext executeContext = new ExecuteContext(chatExecuteReq);
        // SemanticParseInfo parseInfo = chatManageService.getParseInfo(chatExecuteReq.getQueryId(),
        // chatExecuteReq.getParseId());
        Agent agent = agentService.getAgent(chatExecuteReq.getAgentId());
        executeContext.setAgent(agent);
        // executeContext.setParseInfo(parseInfo);
        return executeContext;
    }

    /**
     * 查询数据 根据请求参数执行数据查询操作
     *
     * 处理流程： 1. 获取解析信息 2. 合并请求参数到解析信息 3. 获取数据集架构 4. 创建语义查询对象 5. 根据查询模式分别处理LLM或规则模式 6. 执行查询并返回结果
     *
     * @param chatQueryDataReq 查询数据请求
     * @param user 用户信息
     * @return 查询结果
     * @throws Exception 查询过程中的异常
     */
    @Override
    public Object queryData(ChatQueryDataReq chatQueryDataReq, User user) throws Exception {
        Integer parseId = chatQueryDataReq.getParseId();
        SemanticParseInfo parseInfo =
                chatManageService.getParseInfo(chatQueryDataReq.getQueryId(), parseId);
        mergeParseInfo(parseInfo, chatQueryDataReq);
        DataSetSchema dataSetSchema =
                semanticLayerService.getDataSetSchema(parseInfo.getDataSetId());

        SemanticQuery semanticQuery = QueryManager.createQuery(parseInfo.getQueryMode());
        semanticQuery.setParseInfo(parseInfo);

        if (LLMSqlQuery.QUERY_MODE.equalsIgnoreCase(parseInfo.getQueryMode())) {
            handleLLMQueryMode(chatQueryDataReq, semanticQuery, dataSetSchema, user);
        } else {
            handleRuleQueryMode(semanticQuery, dataSetSchema, user);
        }

        return executeQuery(semanticQuery, user);
    }


    @Override
    public Flux<ExecuteResultEvent> summary(ChatExecuteReq chatExecuteReq, HttpServletResponse response) {

        // 构建执行上下文
        ExecuteContext executeContext = buildExecuteContext(chatExecuteReq);
        // 查询查询结果
        QueryResult queryResult = chatManageService.getQueryResult(chatExecuteReq);
        executeContext.setResponse(queryResult);
        response.setCharacterEncoding("UTF-8");

        if (chatExecuteReq != null) {
            // 异步处理解析逻辑
            SummaryProcessor processor = new DataInterpretProcessor();
                    // 获取流式模型
                    StreamingChatLanguageModel streamingModel = processor.getStreamingModel(executeContext);
                    UserMessage prompt = processor.promt(executeContext);
                    return Flux.create(sink -> {

                        streamingModel.generate(prompt, new StreamingResponseHandler<AiMessage>() {
                            @Override
                            public void onComplete(dev.langchain4j.model.output.Response<AiMessage> response) {
                                log.info("onComplete {}",response);
                                queryResult.setTextSummary(response.content().text());
                                saveSummaryText(queryResult,chatExecuteReq);
                                ExecuteResultEvent executeResultEvent =
                                        new ExecuteResultEvent(
                                                "workflow_finished",
                                                executeContext.getRequest().getQueryId(),
                                                System.currentTimeMillis(),
                                                Map.of("answer", response.content().text()),
                                                "SUCCESS");
                                sink.next(executeResultEvent);
                                sink.complete();
                            }

                            @Override
                            public void onNext(String s) {
                                ExecuteResultEvent executeResultEvent =
                                        new ExecuteResultEvent(
                                                "message",
                                                executeContext.getRequest().getQueryId(),
                                                System.currentTimeMillis(),
                                                Map.of("answer",s),
                                                "SUCCESS");
                                sink.next(executeResultEvent);
                            }

                            @Override
                            public void onError(Throwable throwable) {
                                sink.error(throwable);
                            }
                        });
                    });
        }
        return null;
    }

    @Override
    public ExecuteResultEvent summaryAsync(ChatExecuteReq chatExecuteReq, HttpServletResponse response) {
        // 构建执行上下文
        ExecuteContext executeContext = buildExecuteContext(chatExecuteReq);
        // 查询查询结果
        QueryResult queryResult = chatManageService.getQueryResult(chatExecuteReq);
        executeContext.setResponse(queryResult);
        response.setCharacterEncoding("UTF-8");

        SummaryProcessor processor = new DataInterpretProcessor();
        StreamingChatLanguageModel streamingModel = processor.getStreamingModel(executeContext);
        UserMessage prompt = processor.promt(executeContext);

        CompletableFuture<ExecuteResultEvent> future = new CompletableFuture<>();

        streamingModel.generate(prompt, new StreamingResponseHandler<AiMessage>() {
            private StringBuilder fullResponse = new StringBuilder();

            @Override
            public void onNext(String token) {
                fullResponse.append(token);
            }

            @Override
            public void onComplete(dev.langchain4j.model.output.Response<AiMessage> aiMessageResponse) {
                String summaryText = aiMessageResponse.content().text();
                queryResult.setTextSummary(summaryText);
                saveSummaryText(queryResult, chatExecuteReq);

                future.complete(new ExecuteResultEvent(
                        "workflow_finished",
                        executeContext.getRequest().getQueryId(),
                        System.currentTimeMillis(),
                        Map.of("answer", summaryText),
                        "SUCCESS"
                ));
            }

            @Override
            public void onError(Throwable throwable) {
                future.completeExceptionally(throwable);
            }
        });

        try {
            return future.get(); // 可以加超时限制：future.get(30, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("生成摘要时发生错误", e);
            return new ExecuteResultEvent(
                    "workflow_error",
                    executeContext.getRequest().getQueryId(),
                    System.currentTimeMillis(),
                    Map.of("error", e.getMessage()),
                    "ERROR"
            );
        }
    }


    /**
     * 从SQL中提取字段列表 解析SQL语句，提取所有SELECT部分的字段
     *
     * @param parseInfo 语义解析信息
     * @return 字段名称列表
     */
    private List<String> getFieldsFromSql(SemanticParseInfo parseInfo) {
        SqlInfo sqlInfo = parseInfo.getSqlInfo();
        if (Objects.isNull(sqlInfo) || StringUtils.isNotBlank(sqlInfo.getCorrectedS2SQL())) {
            return new ArrayList<>();
        }
        return SqlSelectHelper.getAllSelectFields(sqlInfo.getCorrectedS2SQL());
    }

    /**
     * 处理大语言模型查询模式 根据LLM生成的SQL进行查询处理
     *
     * 处理流程： 1. 检查是否需要替换指标 2. 根据情况选择重建SQL的方式 3. 重置SQL信息并请求重新翻译
     *
     * @param chatQueryDataReq 查询数据请求
     * @param semanticQuery 语义查询对象
     * @param dataSetSchema 数据集架构
     * @param user 用户信息
     * @throws Exception 处理过程中的异常
     */
    private void handleLLMQueryMode(ChatQueryDataReq chatQueryDataReq, SemanticQuery semanticQuery,
                                    DataSetSchema dataSetSchema, User user) throws Exception {
        SemanticParseInfo parseInfo = semanticQuery.getParseInfo();
        String rebuiltS2SQL;
        if (checkMetricReplace(chatQueryDataReq, parseInfo)) {
            log.info("rebuild S2SQL with adjusted metrics!");
            SchemaElement metricToReplace = chatQueryDataReq.getMetrics().iterator().next();
            rebuiltS2SQL = replaceMetrics(parseInfo, metricToReplace);
            log.info("rebuild SQL:{}",rebuiltS2SQL);
        } else {
            log.info("rebuild S2SQL with adjusted filters!");
            rebuiltS2SQL = replaceFilters(chatQueryDataReq, parseInfo, dataSetSchema);
            log.info("rebuild SQL1:{}",rebuiltS2SQL);

        }
        // reset SqlInfo and request re-translation
        parseInfo.getSqlInfo().setCorrectedS2SQL(rebuiltS2SQL);
        parseInfo.getSqlInfo().setParsedS2SQL(rebuiltS2SQL);
        parseInfo.getSqlInfo().setQuerySQL(null);
        SemanticQueryReq semanticQueryReq = semanticQuery.buildSemanticQueryReq();
        SemanticTranslateResp explain = semanticLayerService.translate(semanticQueryReq, user);
        parseInfo.getSqlInfo().setQuerySQL(explain.getQuerySQL());
    }

    /**
     * 处理规则查询模式 使用规则引擎处理查询，而非LLM生成的SQL
     *
     * 处理流程： 1. 验证维度和指标过滤器 2. 构建S2SQL语句
     *
     * @param semanticQuery 语义查询对象
     * @param dataSetSchema 数据集架构
     * @param user 用户信息
     */
    private void handleRuleQueryMode(SemanticQuery semanticQuery, DataSetSchema dataSetSchema,
                                     User user) {
        log.info("rule begin replace metrics and revise filters!");
        validFilter(semanticQuery.getParseInfo().getDimensionFilters());
        validFilter(semanticQuery.getParseInfo().getMetricFilters());
        semanticQuery.buildS2Sql(dataSetSchema);
    }

    /**
     * 执行语义查询 将语义查询转换为实际数据查询并执行
     *
     * @param semanticQuery 语义查询对象
     * @param user 用户信息
     * @return 查询结果
     * @throws Exception 执行过程中的异常
     */
    private QueryResult executeQuery(SemanticQuery semanticQuery, User user) throws Exception {
        SemanticQueryReq semanticQueryReq = semanticQuery.buildSemanticQueryReq();
        SemanticParseInfo parseInfo = semanticQuery.getParseInfo();
        QueryResult queryResult = doExecution(semanticQueryReq, parseInfo.getQueryMode(), user);
        queryResult.setChatContext(semanticQuery.getParseInfo());
        parseInfo.getSqlInfo().setQuerySQL(queryResult.getQuerySql());
        return queryResult;
    }

    /**
     *  根据SQL查询数据
     * @param querySqlReq
     * @param user
     * @return
     * @throws Exception
     */
     private SemanticQueryResp queryBySql( QuerySqlReq querySqlReq,User user) throws Exception {
         String sql = querySqlReq.getSql();
         querySqlReq.setSql(StringUtil.replaceBackticks(sql));
         chatLayerService.correct(querySqlReq, user);
         return semanticLayerService.queryByReq(querySqlReq, user);

     }
    /**
     * 检查是否需要替换指标 比较SQL中的字段与请求中的指标，判断是否需要进行指标替换
     *
     * @param chatQueryDataReq 查询数据请求
     * @param parseInfo 语义解析信息
     * @return 如果需要替换指标则返回true，否则返回false
     */
    private boolean checkMetricReplace(ChatQueryDataReq chatQueryDataReq,
                                       SemanticParseInfo parseInfo) {
        List<String> oriFields = getFieldsFromSql(parseInfo);
        Set<SchemaElement> metrics = chatQueryDataReq.getMetrics();
        if (CollectionUtils.isEmpty(oriFields) || CollectionUtils.isEmpty(metrics)) {
            return false;
        }
        List<String> metricNames =
                metrics.stream().map(SchemaElement::getName).collect(Collectors.toList());
        return !oriFields.containsAll(metricNames);
    }

    /**
     * 替换过滤条件
     * 根据查询请求重建SQL的过滤条件部分
     *
     * 处理流程：
     * 1. 获取原SQL的WHERE和HAVING表达式
     * 2. 更新WHERE条件
     * 3. 处理日期信息
     * 4. 替换和移除原有条件
     * 5. 更新HAVING条件
     * 6. 添加新的条件
     *
     * @param queryData     查询数据请求
     * @param parseInfo     语义解析信息
     * @param dataSetSchema 数据集架构
     * @return 重建后的SQL语句
     */
    private String replaceFilters(ChatQueryDataReq queryData, SemanticParseInfo parseInfo,
                                  DataSetSchema dataSetSchema) {
        String correctorSql = parseInfo.getSqlInfo().getCorrectedS2SQL();
        log.info("correctorSql before replacing:{}", correctorSql);
            // 检查是否包含 WITH 语句
            if (SqlSelectHelper.hasWith(correctorSql)) {
                return replaceFiltersWithCTE(queryData, parseInfo, dataSetSchema, correctorSql);
            } else {
                return replaceFiltersSimple(queryData, parseInfo, dataSetSchema, correctorSql,0);
            }
    }

    private String replaceFiltersSimple(ChatQueryDataReq queryData, SemanticParseInfo parseInfo, DataSetSchema dataSetSchema, String correctorSql,Integer withIndex) {
//         get where filter and having filter
        List<FieldExpression> whereExpressionList = SqlSelectHelper.getWhereExpression(correctorSql);
        // replace where filter
        // 过滤当前子查询的where 条件
        Set<QueryFilter> dimensionFilters =  queryData.getDimensionFilters().stream().filter(item -> Objects.equals(item.getTableOrder(), withIndex)).collect(Collectors.toSet());
        List<Expression> addWhereConditions = new ArrayList<>();
        Set<String> removeWhereFieldNames = updateFilters(whereExpressionList, dimensionFilters,
                parseInfo.getDimensionFilters(), addWhereConditions);

        Map<String, Map<String, String>> filedNameToValueMap = new HashMap<>();

        Set<String> removeDataFieldNames = updateDateInfo(queryData, parseInfo, dataSetSchema,
                filedNameToValueMap, whereExpressionList, addWhereConditions);

        removeWhereFieldNames.addAll(removeDataFieldNames);

        correctorSql = SqlReplaceHelper.replaceValue(correctorSql, filedNameToValueMap);
        correctorSql = SqlRemoveHelper.removeWhereCondition(correctorSql, removeWhereFieldNames);

        // replace having filter
        List<FieldExpression> havingExpressionList = SqlSelectHelper.getHavingExpressions(correctorSql);
        List<Expression> addHavingConditions = new ArrayList<>();
        Set<String> removeHavingFieldNames = updateFilters(havingExpressionList, dimensionFilters,
                parseInfo.getDimensionFilters(), addHavingConditions);
        correctorSql = SqlReplaceHelper.replaceHavingValue(correctorSql, new HashMap<>());
        correctorSql = SqlRemoveHelper.removeHavingCondition(correctorSql, removeHavingFieldNames);

        correctorSql = SqlAddHelper.addWhere(correctorSql, addWhereConditions);
        correctorSql = SqlAddHelper.addHaving(correctorSql, addHavingConditions);

        return correctorSql;
    }

    /**
     *  替换过滤条件，
     * @param queryData
     * @param parseInfo
     * @param dataSetSchema
     * @param correctorSql
     * @return
     */
    private String replaceFiltersWithCTE(ChatQueryDataReq queryData, SemanticParseInfo parseInfo,
                                         DataSetSchema dataSetSchema, String correctorSql) {
        Integer withIndex = 0;
        // 处理子查询
        Select selectStatement = SqlSelectHelper.getSelect(correctorSql);
        List<WithItem> withItemList = null;
        if (selectStatement != null) {
            withItemList = selectStatement.getWithItemsList();
        }
        if (!CollectionUtils.isEmpty(withItemList)) {
            for (WithItem withItem : withItemList) {
                Select withSelect = withItem.getSelect();
                if (withSelect instanceof ParenthesedSelect parenthesedSelect){
                    Select innerSelect = parenthesedSelect.getSelect();
                    if (innerSelect instanceof PlainSelect plainSelect) {
                        String oldSql = plainSelect.toString();
                        String newSql = replaceFiltersSimple(queryData, parseInfo, dataSetSchema, oldSql,withIndex);
                        // 将修改好的with sql 添加到原sql 中
                        correctorSql = replaceInnerSql(correctorSql , oldSql, newSql);
                    }
                }
                withIndex++;
            }
        }
        // 处理外层查询的过滤条件
        correctorSql = replaceFiltersSimple(queryData, parseInfo, dataSetSchema, correctorSql,0);
        return correctorSql;
    }

    private String replaceInnerSql(String correctorSql, String oldSql, String newSql) {
        return correctorSql.replace(oldSql, newSql);
    }


    /**
     * 替换指标 用新指标替换SQL中的原有指标
     *
     * @param parseInfo 语义解析信息
     * @param metric 要替换的新指标
     * @return 更新后的SQL语句
     */
    private String replaceMetrics(SemanticParseInfo parseInfo, SchemaElement metric) {
        List<String> oriMetrics = parseInfo.getMetrics().stream().map(SchemaElement::getName)
                .collect(Collectors.toList());
        String correctorSql = parseInfo.getSqlInfo().getCorrectedS2SQL();
        log.info("before replaceMetrics:{}", correctorSql);
        log.info("filteredMetrics:{},metrics:{}", oriMetrics, metric);
        Map<String, Pair<String, String>> fieldMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(oriMetrics) && !oriMetrics.contains(metric.getName())) {
            fieldMap.put(oriMetrics.get(0), Pair.of(metric.getName(), metric.getDefaultAgg()));
            correctorSql = SqlReplaceHelper.replaceAggFields(correctorSql, fieldMap);
        }
        log.info("after replaceMetrics:{}", correctorSql);
        return correctorSql;
    }

    /**
     * 执行查询请求 使用语义层服务执行实际的查询请求
     *
     * @param semanticQueryReq 语义查询请求
     * @param queryMode 查询模式
     * @param user 用户信息
     * @return 查询结果，包含数据和元数据
     * @throws Exception 执行过程中的异常
     */
    private QueryResult doExecution(SemanticQueryReq semanticQueryReq, String queryMode, User user)
            throws Exception {
        SemanticQueryResp queryResp = semanticLayerService.queryByReq(semanticQueryReq, user);
        QueryResult queryResult = new QueryResult();

        if (queryResp != null) {
            queryResult.setQueryAuthorization(queryResp.getQueryAuthorization());
            queryResult.setQuerySql(queryResp.getSql());
            queryResult.setQueryResults(queryResp.getResultList());
            queryResult.setQueryColumns(queryResp.getColumns());
        } else {
            queryResult.setQueryResults(new ArrayList<>());
            queryResult.setQueryColumns(new ArrayList<>());
        }

        queryResult.setQueryMode(queryMode);
        queryResult.setQueryState(QueryState.SUCCESS);
        return queryResult;
    }

    private Set<String> updateDateInfo(ChatQueryDataReq queryData, SemanticParseInfo parseInfo,
                                       DataSetSchema dataSetSchema, Map<String, Map<String, String>> filedNameToValueMap,
                                       List<FieldExpression> fieldExpressionList, List<Expression> addConditions) {

        Set<String> removeFieldNames = new HashSet<>();
        if (Objects.isNull(queryData.getDateInfo())) {
            return removeFieldNames;
        }
        if (queryData.getDateInfo().getUnit() > 1) {
            queryData.getDateInfo()
                    .setStartDate(DateUtils.getBeforeDate(queryData.getDateInfo().getUnit() + 1));
            queryData.getDateInfo().setEndDate(DateUtils.getBeforeDate(0));
        }
//        SchemaElement partitionDimension = dataSetSchema.getPartitionDimension();
        // startDate equals to endDate
//        for (FieldExpression fieldExpression : fieldExpressionList) {
//            if (partitionDimension.getName().equals(fieldExpression.getFieldName())) {
//                // first remove,then add
//                removeFieldNames.add(partitionDimension.getName());
//                GreaterThanEquals greaterThanEquals = new GreaterThanEquals();
//                addTimeFilters(queryData.getDateInfo().getStartDate(), greaterThanEquals,
//                        addConditions, partitionDimension);
//                MinorThanEquals minorThanEquals = new MinorThanEquals();
//                addTimeFilters(queryData.getDateInfo().getEndDate(), minorThanEquals, addConditions,
//                        partitionDimension);
//                break;
//            }
//        }
        for (FieldExpression fieldExpression : fieldExpressionList) {
            for (QueryFilter queryFilter : queryData.getDimensionFilters()) {
                if (queryFilter.getOperator().equals(FilterOperatorEnum.LIKE)
                        && FilterOperatorEnum.LIKE.getValue()
                        .equalsIgnoreCase(fieldExpression.getOperator())) {
                    Map<String, String> replaceMap = new HashMap<>();
                    String preValue = fieldExpression.getFieldValue().toString();
                    String curValue = queryFilter.getValue().toString();
                    if (preValue.startsWith("%")) {
                        curValue = "%" + curValue;
                    }
                    if (preValue.endsWith("%")) {
                        curValue = curValue + "%";
                    }
                    replaceMap.put(preValue, curValue);
                    filedNameToValueMap.put(fieldExpression.getFieldName(), replaceMap);
                    break;
                }
            }
        }
        parseInfo.setDateInfo(queryData.getDateInfo());
        return removeFieldNames;
    }

    private <T extends ComparisonOperator> void addTimeFilters(String date, T comparisonExpression,
                                                               List<Expression> addConditions, SchemaElement partitionDimension) {
        Column column = new Column(partitionDimension.getName());
        StringValue stringValue = new StringValue(date);
        comparisonExpression.setLeftExpression(column);
        comparisonExpression.setRightExpression(stringValue);
        addConditions.add(comparisonExpression);
    }

    /**
     * 只更新当前select层的过滤条件
     * 不考虑嵌套select
     * @param fieldExpressionList
     * @param metricFilters
     * @param contextMetricFilters
     * @param addConditions
     * @return
     */
    private Set<String> updateFiltersCurrentSelect(List<FieldExpression> fieldExpressionList,
                                      Set<QueryFilter> metricFilters, Set<QueryFilter> contextMetricFilters,
                                      List<Expression> addConditions) {
        Set<String> removeFieldNames = new HashSet<>();
        if (CollectionUtils.isEmpty(metricFilters)) {
            return removeFieldNames;
        }


        for (int i = 0; i < fieldExpressionList.size(); i++) {
            FieldExpression fieldExpression = fieldExpressionList.get(i);
            List<QueryFilter> metricFilterList = new ArrayList<>(metricFilters);
            QueryFilter queryFilter = metricFilterList.get(i);
            if (fieldExpression.getFieldName() != null
                    && fieldExpression.getFieldName().contains(queryFilter.getName())) {
                removeFieldNames.add(queryFilter.getName());
                handleFilter(queryFilter, contextMetricFilters, addConditions);
                break;
            }
        }
        return removeFieldNames;
    }
    private Set<String> updateFilters(List<FieldExpression> fieldExpressionList,
                                      Set<QueryFilter> metricFilters, Set<QueryFilter> contextMetricFilters,
                                      List<Expression> addConditions) {
        Set<String> removeFieldNames = new HashSet<>();
        if (CollectionUtils.isEmpty(metricFilters)) {
            return removeFieldNames;
        }

        for (QueryFilter dslQueryFilter : metricFilters) {
            for (FieldExpression fieldExpression : fieldExpressionList) {
                if (fieldExpression.getFieldName() != null
                        && fieldExpression.getFieldName().contains(dslQueryFilter.getName())) {
                    removeFieldNames.add(dslQueryFilter.getName());
                    handleFilter(dslQueryFilter, contextMetricFilters, addConditions);
                    break;
                }
            }
        }
        return removeFieldNames;
    }

    private void handleFilter(QueryFilter dslQueryFilter, Set<QueryFilter> contextMetricFilters,
                              List<Expression> addConditions) {
        FilterOperatorEnum operator = dslQueryFilter.getOperator();

        if (operator == FilterOperatorEnum.IN) {
            addWhereInFilters(dslQueryFilter, new InExpression(), contextMetricFilters,
                    addConditions);
        } else {
            ComparisonOperator expression = FilterOperatorEnum.createExpression(operator);
            if (Objects.nonNull(expression)) {
                addWhereFilters(dslQueryFilter, expression, contextMetricFilters, addConditions);
            }
        }
    }

    // add in condition to sql where condition
    private void addWhereInFilters(QueryFilter dslQueryFilter, InExpression inExpression,
                                   Set<QueryFilter> contextMetricFilters, List<Expression> addConditions) {
        Column column = new Column(dslQueryFilter.getName());
        ParenthesedExpressionList parenthesedExpressionList = new ParenthesedExpressionList<>();
        List<String> valueList =
                JsonUtil.toList(JsonUtil.toString(dslQueryFilter.getValue()), String.class);
        if (CollectionUtils.isEmpty(valueList)) {
            return;
        }
        valueList.forEach(o -> {
            StringValue stringValue = new StringValue(o);
            parenthesedExpressionList.add(stringValue);
        });
        inExpression.setLeftExpression(column);
        inExpression.setRightExpression(parenthesedExpressionList);
        addConditions.add(inExpression);
        contextMetricFilters.forEach(o -> {
            if (o.getName().equals(dslQueryFilter.getName())) {
                o.setValue(dslQueryFilter.getValue());
                o.setOperator(dslQueryFilter.getOperator());
            }
        });
    }

    // add where filter
    private void addWhereFilters(QueryFilter dslQueryFilter,
                                 ComparisonOperator comparisonExpression, Set<QueryFilter> contextMetricFilters,
                                 List<Expression> addConditions) {
        String columnName = dslQueryFilter.getName();
        if (StringUtils.isNotBlank(dslQueryFilter.getFunction())) {
            columnName = dslQueryFilter.getFunction() + "(" + dslQueryFilter.getName() + ")";
        }
        if (Objects.isNull(dslQueryFilter.getValue())) {
            return;
        }
        Column column = new Column(columnName);
        comparisonExpression.setLeftExpression(column);
        if (StringUtils.isNumeric(dslQueryFilter.getValue().toString())) {
            LongValue longValue =
                    new LongValue(Long.parseLong(dslQueryFilter.getValue().toString()));
            comparisonExpression.setRightExpression(longValue);
        } else {
            StringValue stringValue = new StringValue(dslQueryFilter.getValue().toString());
            comparisonExpression.setRightExpression(stringValue);
        }
        addConditions.add(comparisonExpression);
        contextMetricFilters.forEach(o -> {
            if (o.getName().equals(dslQueryFilter.getName())) {
                o.setValue(dslQueryFilter.getValue());
                o.setOperator(dslQueryFilter.getOperator());
            }
        });
    }

    /**
     * 合并请求参数到解析信息 将查询请求中的参数合并到语义解析信息中
     *
     * @param parseInfo 语义解析信息
     * @param queryData 查询数据请求
     */
    private void mergeParseInfo(SemanticParseInfo parseInfo, ChatQueryDataReq queryData) {
        if (Objects.nonNull(queryData.getDateInfo())) {
            parseInfo.setDateInfo(queryData.getDateInfo());
        }
        if (LLMSqlQuery.QUERY_MODE.equals(parseInfo.getQueryMode())) {
            return;
        }
        if (!CollectionUtils.isEmpty(queryData.getDimensions())) {
            parseInfo.setDimensions(queryData.getDimensions());
        }
        if (!CollectionUtils.isEmpty(queryData.getMetrics())) {
            parseInfo.setMetrics(queryData.getMetrics());
        }
        if (!CollectionUtils.isEmpty(queryData.getDimensionFilters())) {
            parseInfo.setDimensionFilters(queryData.getDimensionFilters());
            log.info("dimensionFilters2:{}",queryData.getDimensionFilters());

        }
        if (!CollectionUtils.isEmpty(queryData.getMetricFilters())) {
            parseInfo.setMetricFilters(queryData.getMetricFilters());
        }

        parseInfo.setSqlInfo(new SqlInfo());
    }

    /**
     * 验证过滤器 检查并清理无效的过滤条件
     *
     * @param filters 需要验证的过滤器集合
     */
    private void validFilter(Set<QueryFilter> filters) {
        Iterator<QueryFilter> iterator = filters.iterator();
        while (iterator.hasNext()) {
            QueryFilter queryFilter = iterator.next();
            Object queryFilterValue = queryFilter.getValue();
            if (Objects.isNull(queryFilterValue)) {
                iterator.remove();
                continue;
            }
            List<String> collection = new ArrayList<>();
            if (queryFilterValue instanceof List) {
                collection.addAll((List) queryFilterValue);
            } else if (queryFilterValue instanceof String) {
                collection.add((String) queryFilterValue);
            }
            if (FilterOperatorEnum.IN.equals(queryFilter.getOperator())
                    && CollectionUtils.isEmpty(collection)) {
                iterator.remove();
            }
        }
    }

    /**
     * 查询维度值 获取指定维度的可用值列表
     *
     * @param dimensionValueReq 维度值查询请求
     * @param user 用户信息
     * @return 维度值列表
     */
    @Override
    public Object queryDimensionValue(DimensionValueReq dimensionValueReq, User user) {
        Integer agentId = dimensionValueReq.getAgentId();
        Agent agent = agentService.getAgent(agentId);
        dimensionValueReq.setDataSetIds(agent.getDataSetIds());
        return semanticLayerService.queryDimensionValue(dimensionValueReq, user);
    }

    @Override
    public Flux<ChatParseEvent> parseStream(ChatParseReq chatParseReq) {
        return null;
    }

    @Override
    public Flux<ExecuteResultEvent> executeStream(ChatExecuteReq chatExecuteReq) {
        return null;
    }

    /**
     * 保存查询结果 将查询结果保存到历史记录中 注意：只保存第一次解析的查询结果
     *
     * @param chatExecuteReq 查询执行请求
     * @param queryResult 查询结果
     */
    public void saveQueryResult(ChatExecuteReq chatExecuteReq, QueryResult queryResult) {
        // The history record only retains the query result of the first parse
        if (chatExecuteReq.getParseId() > 1) {
            return;
        }
        chatManageService.saveQueryResult(chatExecuteReq, queryResult);
    }

    /**
     * 保存summary总结结果
     *
     * @param chatExecuteReq 查询执行请求
     * @param queryResult 查询结果
     */
    public void saveSummaryText( QueryResult queryResult,ChatExecuteReq chatExecuteReq) {
        // The history record only retains the query result of the first parse
        if (chatExecuteReq.getParseId() > 1) {
            return;
        }
        chatManageService.saveSummaryText(chatExecuteReq, queryResult);
    }

    private int estimateTokenCount(String text) {
        // 简单估算：按空格、标点符号分词
        String[] words = text.split("[\\s.,;?!]+");
        return words.length;
    }
}
