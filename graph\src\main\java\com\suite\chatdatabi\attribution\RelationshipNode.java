package com.suite.chatdatabi.attribution;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.fastjson.JSON;
import com.suite.chatdatabi.chat.server.service.GraphQueryService;
import com.suite.chatdatabi.enmu.ErrorType;
import com.suite.chatdatabi.headless.server.persistence.dataobject.RelationshipDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;


@Slf4j
public class RelationshipNode implements NodeAction {

    private GraphQueryService graphQueryService;

    public RelationshipNode(GraphQueryService graphQueryService) {
        this.graphQueryService = graphQueryService;
    }


    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {

        try {
            Set<String> result = (Set<String>) state.value("segmentResult").get();
            Integer queryAgentId = Integer.valueOf(state.value("agentId").get().toString());

            // json 数组字符串转 list
            if (result.isEmpty()){
                return Map.of("relationshipResult",new ArrayList<>());
            }
            List<RelationshipDO> relationshipDOS = graphQueryService.queryRelationshipDataByKeyWord(new ArrayList<>(result),queryAgentId);

            return Map.of("relationshipResult",JSON.toJSONString(relationshipDOS));
        }catch (Exception e){
            return Map.of("error", ErrorType.RELATIONSHIP_ERROR);
        }
    }
}
