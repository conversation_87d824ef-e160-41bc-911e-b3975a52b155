// SYSTEM_ADMIN超管 ADMIN管理员
export const ROUTE_AUTH_CODES = { SYSTEM_ADMIN: 'SYSTEM_ADMIN', ADMIN: 'ADMIN' };

const ENV_KEY = {
  CHAT: 'chat',
  SEMANTIC: 'semantic',
};

const { APP_TARGET } = process.env;

const ROUTES = [
  {
    path: '/chat/mobile',
    name: 'chat',
    component: '@/pages/ChatPage',
    hideInMenu: true,
    layout: false,
    envEnableList: [ENV_KEY.CHAT],
  },
  {
    path: '/chat/external',
    name: 'chat',
    component: '@/pages/ChatPage',
    hideInMenu: true,
    layout: false,
    envEnableList: [ENV_KEY.CHAT],
  },
  {
    path: '/chat',
    name: 'chat',
    component: '@/pages/ChatPage',
    envEnableList: [ENV_KEY.CHAT],
  },
  // {
  //   path: '/chatSetting/model/:domainId?/:modelId?/:menuKey?',
  //   component: '@/pages/SemanticModel/ChatSetting/ChatSetting',
  //   name: 'chatSetting',
  //   envEnableList: [ENV_KEY.CHAT],
  // },
  {
    path: '/agent',
    name: 'agent',
    component: '@/pages/Agent',
    envEnableList: [ENV_KEY.CHAT],
    access: ROUTE_AUTH_CODES.ADMIN,
  },
  // {
  //   path: '/plugin',
  //   name: 'plugin',
  //   component: '@/pages/ChatPlugin',
  //   envEnableList: [ENV_KEY.CHAT],
  //   access: ROUTE_AUTH_CODES.ADMIN,
  // },
  {
    path: '/model/metric/edit/:metricId',
    name: 'metricEdit',
    hideInMenu: true,
    component: '@/pages/SemanticModel/Metric/Edit',
    envEnableList: [ENV_KEY.SEMANTIC],
  },
  {
    path: '/model/',
    component: '@/pages/SemanticModel',
    name: 'semanticModel',
    envEnableList: [ENV_KEY.SEMANTIC],
    access: ROUTE_AUTH_CODES.ADMIN,
    routes: [
      {
        path: '/model/',
        redirect: '/model/domain',
      },
      {
        path: '/model/domain/',
        component: '@/pages/SemanticModel/OverviewContainer',
        access: ROUTE_AUTH_CODES.ADMIN,
        routes: [
          {
            path: '/model/domain/:domainId',
            component: '@/pages/SemanticModel/DomainManager',
            routes: [
              {
                path: '/model/domain/:domainId/:menuKey',
                component: '@/pages/SemanticModel/DomainManager',
              },
            ],
          },
          {
            path: '/model/domain/manager/:domainId/:modelId',
            component: '@/pages/SemanticModel/ModelManager',
            routes: [
              {
                path: '/model/domain/manager/:domainId/:modelId/:menuKey',
                component: '@/pages/SemanticModel/ModelManager',
              },
            ],
          },
        ],
      },
      {
        path: '/model/dataset/:domainId/:datasetId',
        component: '@/pages/SemanticModel/View/components/Detail',
        envEnableList: [ENV_KEY.SEMANTIC],
        routes: [
          {
            path: '/model/dataset/:domainId/:datasetId/:menuKey',
            component: '@/pages/SemanticModel/View/components/Detail',
          },
        ],
      },
      {
        path: '/model/metric/:domainId/:modelId/:metricId',
        component: '@/pages/SemanticModel/Metric/Edit',
        envEnableList: [ENV_KEY.SEMANTIC],
        // routes: [
        //   {
        //     path: '/model/manager/:domainId/:modelId/:menuKey',
        //     component: '@/pages/SemanticModel/ModelManager',
        //   },
        // ],
      },
      {
        path: '/model/dimension/:domainId/:modelId/:dimensionId',
        component: '@/pages/SemanticModel/Dimension/Detail',
        envEnableList: [ENV_KEY.SEMANTIC],
        // routes: [
        //   {
        //     path: '/model/manager/:domainId/:modelId/:menuKey',
        //     component: '@/pages/SemanticModel/ModelManager',
        //   },
        // ],
      },
    ],
  },

  // {
  //   path: '/metric',
  //   name: 'metric',
  //   component: '@/pages/SemanticModel/Metric',
  //   envEnableList: [ENV_KEY.SEMANTIC],
  //   access: ROUTE_AUTH_CODES.ADMIN,
  //   routes: [
  //     {
  //       path: '/metric',
  //       redirect: '/metric/market',
  //     },
  //     {
  //       path: '/metric/market',
  //       component: '@/pages/SemanticModel/Metric/Market',
  //       access: ROUTE_AUTH_CODES.ADMIN,
  //       hideInMenu: true,
  //       envEnableList: [ENV_KEY.SEMANTIC],
  //     },
  //     {
  //       path: '/metric/detail/:metricId',
  //       name: 'metricDetail',
  //       hideInMenu: true,
  //       component: '@/pages/SemanticModel/Metric/Detail',
  //       envEnableList: [ENV_KEY.SEMANTIC],
  //     },
  //     {
  //       path: '/metric/detail/edit/:metricId',
  //       name: 'metricDetail',
  //       hideInMenu: true,
  //       component: '@/pages/SemanticModel/Metric/Edit',
  //       envEnableList: [ENV_KEY.SEMANTIC],
  //     },
  //   ],
  // },
  {
    path: '/tag',
    name: 'tag',
    component: '@/pages/SemanticModel/Insights',
    envEnableList: [ENV_KEY.SEMANTIC],
    hideInMenu: process.env.SHOW_TAG ? false : true,
    routes: [
      {
        path: '/tag',
        redirect: '/tag/market',
      },
      {
        path: '/tag/market',
        component: '@/pages/SemanticModel/Insights/Market',
        hideInMenu: true,
        envEnableList: [ENV_KEY.SEMANTIC],
      },
      {
        path: '/tag/detail/:tagId',
        name: 'tagDetail',
        hideInMenu: true,
        component: '@/pages/SemanticModel/Insights/Detail',
        envEnableList: [ENV_KEY.SEMANTIC],
      },
    ],
  },

  {
    path: '/login',
    name: 'login',
    layout: false,
    hideInMenu: true,
    component: './Login', // 登录页保持同步加载，确保首屏快速显示
  },
  // {
  //   path: '/database',
  //   name: 'database',
  //   component: '@/pages/SemanticModel/components/Database/DatabaseTable',
  //   envEnableList: [ENV_KEY.SEMANTIC],
  //   access: ROUTE_AUTH_CODES.ADMIN,
  // },
  {
    path: '/llm',
    name: 'llm',
    component: '@/pages/SemanticModel/components/LLM/LlmTable',
    envEnableList: [ENV_KEY.SEMANTIC],
    access: ROUTE_AUTH_CODES.ADMIN,
  },
  {
    path: '/system',
    name: 'system',
    component: '@/pages/System',
    access: ROUTE_AUTH_CODES.SYSTEM_ADMIN,
  },
  // {
  //   path: '/user',
  //   name: 'user',
  //   component: '@/pages/User/UserTable',
  //   access: ROUTE_AUTH_CODES.ADMIN,
  // },
  // {
  //   path: '/prompt',
  //   name: 'prompt',
  //   component: '@/pages/Prompt',
  //   access: ROUTE_AUTH_CODES.ADMIN,
  // },
  {
    path: '/',
    redirect: '/chat',
  },
  {
    path: '/401',
    component: '@/pages/401',
  },
];

export default ROUTES;
