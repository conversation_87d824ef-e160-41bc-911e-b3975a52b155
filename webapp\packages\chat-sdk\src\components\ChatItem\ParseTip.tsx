import React, { ReactNode, useState } from 'react';
import { ChatContextType } from '../../common/type';
import { CheckCircleFilled, CloseCircleFilled, UpOutlined, DownOutlined } from '@ant-design/icons';
import Loading from './Loading';
import classNames from 'classnames';
import { prefixCls } from './ParseTipUtils';

type Props = {
  parseLoading: boolean;
  parseTip: string;
  currentParseInfo?: ChatContextType;
  parseTimeCost?: number;
  isDeveloper?: boolean;
  isSimpleMode?: boolean;
};

const ParseTip: React.FC<Props> = ({
  isSimpleMode = false,
  parseLoading,
  parseTip,
  currentParseInfo,
  parseTimeCost,
  isDeveloper,
}) => {


  // 创建简化的指标和维度显示节点
  const getSimplifiedTipNode = () => {
    const { dimensions, metrics } = currentParseInfo || {};

    const hasMetrics = Array.isArray(metrics) && metrics.length > 0;
    const hasDimensions = Array.isArray(dimensions) && dimensions.length > 0;

    // 维度和指标都没有数据时，显示“无数据”
    if (!hasMetrics && !hasDimensions) {
      return (
        <div className={`${prefixCls}-tip-content`}>
          <div className={`${prefixCls}-tip-item`}>
            <div className={`${prefixCls}-tip-item-value`} style={{ color: 'rgb(98, 106, 106)' }}>无数据</div>
          </div>
        </div>
      );
    }

    return (
      <div className={`${prefixCls}-tip-content`}>
        {/* 显示维度 */}
        {hasDimensions && (
          <div className={`${prefixCls}-tip-item`}>
            <div className={`${prefixCls}-tip-item-name`}>
              <span style={{ marginRight: '8px' }}>•</span>
              维度：
            </div>
            <div className={`${prefixCls}-tip-item-value`}>
              {dimensions!.map(dimension => dimension.name).join('、')}
            </div>
          </div>
        )}

        {/* 显示指标 */}
        {hasMetrics && (
          <div className={`${prefixCls}-tip-item`}>
            <div className={`${prefixCls}-tip-item-name`}>
              <span style={{ marginRight: '8px' }}>•</span>
              指标：
            </div>
            <div className={`${prefixCls}-tip-item-value`}>
              {metrics!.map(metric => metric.name).join('、')}
            </div>
          </div>
        )}
      </div>
    );
  };

  const [collapsed, setCollapsed] = useState(false);

  const getNode = (tipTitle: ReactNode, tipNode?: ReactNode, failed?: boolean) => {
    const isLoading = tipNode === undefined; // 当tipNode为undefined时表示正在加载
    return (
      <div className={classNames(`${prefixCls}-parse-tip`, failed && `${prefixCls}-parse-tip-failed`)}>
        <div className={`${prefixCls}-title-bar`}>
          {!failed ? (
            <CheckCircleFilled className={`${prefixCls}-step-icon`} />
          ) : (
            <CloseCircleFilled className={`${prefixCls}-step-error-icon`} />
          )}
          <div className={`${prefixCls}-step-title`}>
            {tipTitle}
            {tipNode === undefined && <Loading />}
          </div>
          {/* 只有在非加载状态时才显示箭头按钮 */}
          {!isLoading && (
            <span
              onClick={() => setCollapsed(!collapsed)}
              style={{ cursor: 'pointer', marginLeft: 8, color: 'var(--text-color-third)' }}
            >
              {collapsed ? <DownOutlined /> : <UpOutlined />}
            </span>
          )}
        </div>
        {(tipNode || tipNode === null) && !collapsed && (
          <div
            className={classNames(
              `${prefixCls}-content-container`,
              tipNode === null && `${prefixCls}-empty-content-container`,
              failed && `${prefixCls}-content-container-failed`
            )}
          >
            {tipNode}
          </div>
        )}
      </div>
    );
  };

  if (parseLoading) {
    return getNode('维度指标中');
  }

  if (parseTip) {
    return getNode(
      <>
        维度指标解析失败
        {!!parseTimeCost && isDeveloper && (
          <span className={`${prefixCls}-title-tip`}>(耗时: {parseTimeCost}ms)</span>
        )}
      </>,
      parseTip,
      true
    );
  }

  if (isSimpleMode || !currentParseInfo) {
    return null;
  }

  const { queryMode } = currentParseInfo || {};

  const tipNode = (
    <div className={`${prefixCls}-tip`}>
      {getSimplifiedTipNode()}
    </div>
  );

  return getNode(
    <>
      维度指标
      {!!parseTimeCost && isDeveloper && (
        <span className={`${prefixCls}-title-tip`}>(耗时: {parseTimeCost}ms)</span>
      )}
    </>,
    queryMode === 'PLAIN_TEXT' ? null : tipNode
  );
};

export default ParseTip;
