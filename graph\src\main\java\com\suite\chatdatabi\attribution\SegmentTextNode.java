package com.suite.chatdatabi.attribution;


import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.cloud.ai.graph.exception.GraphRunnerException;
import com.alibaba.cloud.ai.graph.exception.RunnableErrors;
import com.suite.chatdatabi.chat.server.service.GraphQueryService;
import com.suite.chatdatabi.enmu.ErrorType;
import lombok.extern.slf4j.Slf4j;


import java.util.Map;
import java.util.Optional;
import java.util.Set;


@Slf4j
public class SegmentTextNode implements NodeAction {

    private GraphQueryService graphQueryService;

    public SegmentTextNode(GraphQueryService graphQueryService) {
        this.graphQueryService = graphQueryService;
    }

    @Override
    public Map<String, Object> apply(OverAllState state) {

        try {
            Optional<Object> queryText = state.value("queryText");
            Optional<Object> agentId = state.value("agentId");

            if (queryText.isEmpty() || agentId.isEmpty()) {
                return Map.of();
            }

            Set<String> strings = graphQueryService.segmentText(queryText.get().toString(), Integer.valueOf(agentId.get().toString()));
            log.info("SegmentTextNode 获取到 queryText ------>{}",queryText);
            log.info("SegmentTextNode 获取到 segmentResult ------>{}",strings);
            return Map.of("segmentResult",strings);
        }catch (Exception e){
            return Map.of("error", ErrorType.SEGMENT_ERROR);
        }

    }
}
