package com.suite.chatdatabi.attribution;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.fastjson.JSON;
import com.suite.chatdatabi.chat.server.service.GraphQueryService;
import com.suite.chatdatabi.common.pojo.User;
import com.suite.chatdatabi.enmu.ErrorType;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

public class NL2DataNode implements NodeAction {

    private final GraphQueryService graphQueryService;

    private static final ExecutorService executorService = Executors.newFixedThreadPool(3);

    public NL2DataNode(GraphQueryService graphQueryService) {
        this.graphQueryService = graphQueryService;
    }

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {

//        try {
            Object lineageSplittingResult =  state.value("lineageSplittingResult").orElse( null);

            User user =  state.value("user", User.class).orElse( null);

            if (lineageSplittingResult == null) {
                return Map.of("nl2DataResult", new ArrayList<>());
            }
            List<String> lineageSplittingResultList = JSON.parseArray(String.valueOf(lineageSplittingResult), String.class);
            Integer agentId = (Integer)state.value("agentId").get();
            List<CompletableFuture<String>> futures = lineageSplittingResultList.stream().map(item -> CompletableFuture.supplyAsync(() -> getNl2Data(item,agentId,user),executorService)).toList();
            CompletableFuture<Void> allDone = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );
            List<String> result = allDone.thenApply(
                    v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList())).join();

            return Map.of("nl2DataResult",result);
//        } catch (Exception e){
//            return Map.of("error", ErrorType.NL2DATA_ERROR);
//        }
    }


    private String getNl2Data(String queryText,Integer agentId,User user) {
      return  graphQueryService.getNl2Data(queryText,agentId,user);
    }
}
