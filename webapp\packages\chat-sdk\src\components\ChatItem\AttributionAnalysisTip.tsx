import React, { ReactNode } from 'react';
import { CheckCircleFilled, CloseCircleFilled, DownOutlined, RightOutlined } from '@ant-design/icons';
import Loading from './Loading';
import MarkdownRenderer from '../MarkdownRenderer';
import classNames from 'classnames';
import { prefixCls } from './ParseTipUtils';
import { isMobile } from '../../utils/utils';

type Props = {
  loading: boolean;
  streamingData: string;
  staticData?: string; // 用于页面刷新时显示静态数据
  failed?: boolean;
  errorMsg?: string;
  timeCost?: number;
  isDeveloper?: boolean;
};

const AttributionAnalysisTip: React.FC<Props> = ({
  loading,
  streamingData,
  staticData,
  failed = false,
  errorMsg,
  timeCost,
  isDeveloper,
}) => {
  const contentClass = classNames(`${prefixCls}-content`, {
    [`${prefixCls}-content-mobile`]: isMobile,
  });

  const [collapsed, setCollapsed] = React.useState(false);

  const getNode = (tipTitle: ReactNode, tipNode?: ReactNode, failed?: boolean) => {
    const isLoading = tipNode === undefined; // 当tipNode为undefined时表示正在加载
    return (
      <div className={classNames(`${prefixCls}-parse-tip`, failed && `${prefixCls}-parse-tip-failed`)}>
        <div className={`${prefixCls}-title-bar`}>
          {!failed ? (
            <CheckCircleFilled className={`${prefixCls}-step-icon`} />
          ) : (
            <CloseCircleFilled className={`${prefixCls}-step-error-icon`} />
          )}
          <div className={`${prefixCls}-step-title`}>
            {tipTitle}
            {tipNode === undefined && <Loading />}
          </div>
          {/* 只有在非加载状态时才显示箭头按钮 */}
          {!isLoading && (
            <span
              onClick={() => setCollapsed(!collapsed)}
              style={{ cursor: 'pointer', marginLeft: 8, color: 'var(--text-color-third)', fontSize: '12px' }}
            >
              {collapsed ? <RightOutlined /> : <DownOutlined />}
            </span>
          )}
        </div>
        {(tipNode || tipNode === null) && !collapsed && (
          <div
            className={classNames(
              `${prefixCls}-content-container`,
              tipNode === null && `${prefixCls}-empty-content-container`,
              failed && `${prefixCls}-content-container-failed`
            )}
          >
            {tipNode}
          </div>
        )}
      </div>
    );
  };

  if (loading && !streamingData) {
    return getNode('归因分析中');
  }

  if (failed) {
    return getNode(
      <>
        归因分析失败
        {!!timeCost && isDeveloper && (
          <span className={`${prefixCls}-title-tip`}>(耗时: {timeCost}ms)</span>
        )}
      </>,
      errorMsg || '归因分析失败，请重试',
      true
    );
  }

  // 优先显示流式数据，如果没有则显示静态数据
  const displayData = streamingData || staticData;

  const tipNode = displayData ? (
    <div className={contentClass}>
        <MarkdownRenderer text={displayData} />
    </div>
  ) : null;

  return getNode(
    <>
      归因分析
      {!!timeCost && isDeveloper && (
        <span className={`${prefixCls}-title-tip`}>(耗时: {timeCost}ms)</span>
      )}
    </>,
    tipNode
  );
};

export default AttributionAnalysisTip;
