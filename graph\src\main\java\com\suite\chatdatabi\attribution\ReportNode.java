package com.suite.chatdatabi.attribution;

import com.alibaba.cloud.ai.graph.NodeOutput;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.cloud.ai.graph.async.AsyncGenerator;
import com.alibaba.cloud.ai.graph.streaming.StreamingChatGenerator;
import com.alibaba.fastjson.JSON;
import com.suite.chatdatabi.chat.api.pojo.request.ChatParseReq;
import com.suite.chatdatabi.chat.api.pojo.response.QueryResult;
import com.suite.chatdatabi.chat.server.persistence.dataobject.ChatQueryDO;
import com.suite.chatdatabi.chat.server.service.GraphQueryService;
import com.suite.chatdatabi.enmu.ErrorType;
import com.suite.chatdatabi.headless.api.pojo.response.QueryState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.PromptTemplate;
import reactor.core.publisher.Flux;

import java.util.Map;

@Slf4j
public class ReportNode implements NodeAction {

    private ChatClient client;

    private GraphQueryService graphQueryService;

    private static final PromptTemplate DEFAULT_PROMPT_TEMPLATE = new PromptTemplate(
            """
            你将使用我提供的信息对用户的问题进行归因分析，并以 markdown 格式返回结果。
            首先，请仔细阅读以下数据库表的 schema 信息：
            <schema_info>
            {SCHEMA_INFO}
            </schema_info>
            接着，查看以下血缘关系的结构信息：
            <blood_relation_structure>
            {BLOOD_RELATION_STRUCTURE}
            </blood_relation_structure>
            然后，了解血缘关系的查询结果：
            <blood_relation_query_result>
            {BLOOD_RELATION_QUERY_RESULT}
            </blood_relation_query_result>
            这是用户的问题：
            <user_question>
            {USER_QUESTION}
            </user_question>
            在进行归因分析时，请按照以下步骤进行：
            1. 仔细研读数据库表的 schema 信息、血缘关系的结构信息和查询结果。
            2. 将这些信息与用户的问题相结合，分析可能的原因。
            3. 考虑各种因素之间的关联和影响。
            4. 综合所有信息形成归因分析结果。
            5. 如果需要表格展示数据的详情，请用表格展示。
            5. 直接输出内容，不要添加任何外层包裹标记（如 ```markdown 或 ```）
            6. 只回答结果，不展示思考过程。
            <回答>
            [在此以 markdown 格式输出归因分析结果]
            </回答>

            """
    );
    public ReportNode(ChatClient.Builder chatClientBuilder,GraphQueryService graphQueryService) {
        this.client = chatClientBuilder.build();
        this.graphQueryService = graphQueryService;
    }

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {

        try {
            Object queryText =  state.value("queryText").orElse( null);
            Object schemaResult =  state.value("schemaResult").orElse( null);
            Object relationshipResult =  state.value("relationshipResult").orElse( null);
            Object nl2DataResult =  state.value("nl2DataResult").orElse( null);

            Flux<ChatResponse> streamResult = this.client.prompt().user(
                            (user) ->
                                    user.text(DEFAULT_PROMPT_TEMPLATE.getTemplate())
                                            .param("SCHEMA_INFO", schemaResult)
                                            .param("BLOOD_RELATION_STRUCTURE", relationshipResult)
                                            .param("USER_QUESTION", queryText)
                                            .param("BLOOD_RELATION_QUERY_RESULT",nl2DataResult)
                    ).stream()
                    .chatResponse()
                    .doOnError(error -> {
                        throw new RuntimeException(error);
                    });
            AsyncGenerator<? extends NodeOutput> generator = StreamingChatGenerator.builder()
                    .startingNode("report_stream")
                    .startingState(state)
                    .mapResult(response -> {
                        String text = response.getResult().getOutput().getText();
                        return Map.of("reportResult", text);
                    })
                    .build(streamResult);
            return Map.of("reportResult",generator);
        }catch (Exception e){
            return Map.of("error", ErrorType.REPORT_ERROR);
        }
    }

}
