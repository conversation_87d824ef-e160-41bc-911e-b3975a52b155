package com.suite.chatdatabi.config;

import com.alibaba.cloud.ai.graph.KeyStrategy;
import com.alibaba.cloud.ai.graph.KeyStrategyFactory;
import com.alibaba.cloud.ai.graph.StateGraph;
import com.alibaba.cloud.ai.graph.action.AsyncEdgeAction;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;
import com.alibaba.cloud.ai.graph.state.strategy.ReplaceStrategy;
import com.suite.chatdatabi.attribution.*;
import com.suite.chatdatabi.chat.server.service.GraphQueryService;
import com.suite.chatdatabi.process.RouteMapFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

import static com.alibaba.cloud.ai.graph.action.AsyncNodeAction.node_async;


@Configuration
public class AttributionGraphConfig {


    @Autowired
    GraphQueryService graphQueryService;


    @Bean
    public StateGraph attributionGraph(ChatClient.Builder chatClientBuilder) throws GraphStateException {
        KeyStrategyFactory keyStrategyFactory = ()-> {

            Map<String, KeyStrategy> keyHashMap  = new HashMap<>();
            keyHashMap.put("queryText",new ReplaceStrategy());
            keyHashMap.put("user",new ReplaceStrategy());
            keyHashMap.put("agentId",new ReplaceStrategy());
            keyHashMap.put("chatId",new ReplaceStrategy());
            keyHashMap.put("segmentResult",new ReplaceStrategy());
            keyHashMap.put("relationshipResult",new ReplaceStrategy());
            keyHashMap.put("schemaResult",new ReplaceStrategy());
            keyHashMap.put("lineageSplittingResult",new ReplaceStrategy());
            keyHashMap.put("nl2DataResult",new ReplaceStrategy());
            keyHashMap.put("reportResult",new ReplaceStrategy());

            keyHashMap.put("error",new ReplaceStrategy());

            return keyHashMap;
        };



        StateGraph stateGraph = new StateGraph(keyStrategyFactory);
        stateGraph
                .addNode("schema",node_async(new SchemaNode(graphQueryService)))
                .addNode("segment", node_async(new SegmentTextNode(graphQueryService)))
                .addNode("relationship", node_async(new RelationshipNode(graphQueryService)))
                .addNode("lineageSplitting", node_async(new LineageSplittingNode(chatClientBuilder)))
                .addNode("nl2data", node_async(new NL2DataNode(graphQueryService)))
                .addNode("report", node_async(new ReportNode(chatClientBuilder,graphQueryService)))
                .addNode("save",node_async(new SaveReportNode(graphQueryService)))


                .addEdge(StateGraph.START,"segment")
                .addConditionalEdges("segment",
                        AsyncEdgeAction.edge_async(new ErrorConditionalNode()), RouteMapFactory.createErrorHandlingMap("relationship"))
                .addConditionalEdges("relationship",
                        AsyncEdgeAction.edge_async(new ErrorConditionalNode()), RouteMapFactory.createErrorHandlingMap("schema"))
                .addConditionalEdges("schema",
                        AsyncEdgeAction.edge_async(new ErrorConditionalNode()), RouteMapFactory.createErrorHandlingMap("lineageSplitting"))
                .addConditionalEdges("lineageSplitting",
                        AsyncEdgeAction.edge_async(new ErrorConditionalNode()), RouteMapFactory.createErrorHandlingMap("nl2data"))
                .addEdge("nl2data",
                        "report")
                .addEdge("report","save")
                .addEdge("save",StateGraph.END);

        return stateGraph;
    }
}
