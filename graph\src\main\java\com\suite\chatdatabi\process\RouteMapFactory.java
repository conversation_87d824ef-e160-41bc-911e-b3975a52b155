package com.suite.chatdatabi.process;

import com.alibaba.cloud.ai.graph.StateGraph;
import com.suite.chatdatabi.enmu.ErrorType;

import java.util.Map;

public class RouteMapFactory {
    public static Map<String, String> createErrorHandlingMap(String nextNode) {
        return  Map.of(
                ErrorType.OK.toString(), nextNode,
                ErrorType.SEGMENT_ERROR.toString(), StateGraph.END,
                ErrorType.SCHEMA_ERROR.toString(), StateGraph.END,
                ErrorType.NL2DATA_ERROR.toString(), StateGraph.END,
                ErrorType.RELATIONSHIP_ERROR.toString(), StateGraph.END,
                ErrorType.LINEAGE_SPLITTING_ERROR.toString(), StateGraph.END,
                ErrorType.REPORT_ERROR.toString(), StateGraph.END,
                ErrorType.SAVE_ERROR.toString(), StateGraph.END
        );
    }

}