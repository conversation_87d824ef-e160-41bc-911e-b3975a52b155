package com.suite.chatdatabi.attribution;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.EdgeAction;
import com.suite.chatdatabi.enmu.ErrorType;

public class ErrorConditionalNode implements EdgeAction {
    @Override
    public String apply(OverAllState state) throws Exception {

        Object error = state.value("error").orElse(null);

        String errorStr = String.valueOf(error);
        if (ErrorType.NL2DATA_ERROR.toString().equals(errorStr)){
            return ErrorType.NL2DATA_ERROR.toString();
        }
        if (ErrorType.SEGMENT_ERROR.toString().equals(errorStr)){
            return ErrorType.SEGMENT_ERROR.toString();
        }
        if (ErrorType.SCHEMA_ERROR.toString().equals(errorStr)){
            return ErrorType.SCHEMA_ERROR.toString();
        }
        if (ErrorType.RELATIONSHIP_ERROR.toString().equals(errorStr)){
            return ErrorType.RELATIONSHIP_ERROR.toString();
        }
        if (ErrorType.LINEAGE_SPLITTING_ERROR.toString().equals(errorStr)){
            return ErrorType.LINEAGE_SPLITTING_ERROR.toString();
        }
        if (ErrorType.REPORT_ERROR.toString().equals(errorStr)){
            return ErrorType.REPORT_ERROR.toString();
        }
        if (ErrorType.SAVE_ERROR.toString().equals(errorStr)){
            return ErrorType.SAVE_ERROR.toString();
        }
        return ErrorType.OK.toString();
    }
}
